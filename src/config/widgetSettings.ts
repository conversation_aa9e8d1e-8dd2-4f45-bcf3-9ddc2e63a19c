import { ButtonWidgetProcessor } from '../widgets/buttonWidgetProcessor';
import { DualColorHeadingWidgetProcessor } from '../widgets/dualColorHeadingWidgetProcessor';
import { CreativeButtonWidgetProcessor } from '../widgets/creativeButtonWidgetProcessor';
import { CtaBoxWidgetProcessor } from '../widgets/ctaBoxWidgetProcessor';
import { CounterWidgetProcessor } from '../widgets/counterWidgetProcessor';
import { HeadingWidgetProcessor } from '../widgets/headingWidgetProcessor';
import { TextEditorWidgetProcessor } from '../widgets/textEditorWidgetProcessor';
import { FeatureListWidgetProcessor } from '../widgets/featureListWidgetProcessor';
import { InfoBoxWidgetProcessor } from '../widgets/infoBoxWidgetProcessor';
import { TestimonialWidgetProcessor } from '../widgets/testimonialWidgetProcessor';
import { AdvancedMenuWidgetProcessor } from '../widgets/advancedMenuWidgetProcessor';
import { ImageWidgetProcessor } from '../widgets/imageWidgetProcessor';
import { PostCarouselWidgetProcessor } from '../widgets/postCarouselWidgetProcessor';
import { LogoCarouselWidgetProcessor } from '../widgets/logoCarouselWidgetProcessor';
// import { WooProductListWidgetProcessor } from '../widgets/wooProductListWidgetProcessor';

type WidgetSetting = {
  typography?: Record<string, string>;
  color?: Record<string, string>;
  background?: Record<string, string>;
  borderRadius?: Record<string, string>;
  borderWidth?: Record<string, string>;
  padding?: Record<string, string>;
  margin?: Record<string, string>;
};

export const widgetSettings: Record<string, WidgetSetting> = {
  'button': {
    typography: {
      'button-text': 'typography',
    },
    color: {
      'button-text': 'button_text_color',
    },
    background: {
      'button-container': 'background_color'
    }
  },
  'eael-advanced-menu': {
    typography: {
      'eael-advanced-menu-item': 'default_eael_advanced_menu_item_typography',
    },
    color: {
      'eael-advanced-menu-item': 'default_eael_advanced_menu_item_color',
      'eael-advanced-menu-item-hover': 'default_eael_advanced_menu_item_color_hover'
    },
    background: {
      'eael-advanced-menu-container': 'default_eael_advanced_menu_background',
      'eael-advanced-menu-item-hover': 'default_eael_advanced_menu_item_background_hover'
    }
  },
  'eael-counter': {
    typography: {
      'eael-counter-number': 'counter_num_typography',
      'eael-counter-title': 'counter_title_typography',
      'eael-counter-suffix': 'section_number_suffix_typography',
    },
    color: {
      'eael-counter-title': 'counter_title_color',
      'eael-counter-number': 'counter_num_color',
    },
    background: {
      'eael-counter-container': '_background_color'
    },
    borderRadius: {
      'eael-counter-icon': 'counter_icon_border_radius',
      'eael-counter-container': '_border_radius'
    },
    padding: {
      'eael-counter-icon': 'counter_icon_padding',
    },
    margin: {
      'eael-counter-icon': 'counter_icon_margin',
    }
  },
  'eael-creative-button': {
    typography: {
      'eael-creative-button-text': 'eael_creative_button_typography',
    },
    color: {
      'eael-creative-button-text': 'eael_creative_button_text_color',
    },
    background: {
      'eael-creative-button-container': 'eael_creative_button_background_color'
    },
    borderRadius: {
      'eael-creative-button-container': 'eael_creative_button_border_radius'
    }
  },

  'eael-cta-box': {
    typography: {
      'eael-cta-box-title': 'eael_cta_title_typography',
      'eael-cta-box-content': 'eael_cta_content_typography',
      'eael-cta-box-button': 'eael_cta_btn_typography',
      'eael-cta-box-button-secondary': 'eael_cta_secondary_btn_typography',
    },
    color: {
      'eael-cta-box-title': 'eael_cta_title_color',
      'eael-cta-box-content': 'eael_cta_content_color',
      'eael-cta-box-button': 'eael_cta_btn_normal_text_color',
      'eael-cta-box-button-secondary': 'eael_cta_secondary_btn_normal_text_color',
    },
    background: {
      'eael-cta-box-container': 'eael_cta_bg_color',
      'eael-cta-box-button': 'eael_cta_btn_normal_bg_color',
      'eael-cta-box-button-secondary': 'eael_cta_secondary_btn_normal_bg_color_color',
    },
    borderRadius: {
      'eael-cta-box-button': 'eael_cta_btn_border_radius',
      'eael-cta-box-container': 'eael_cta_border_radius'
    },
    borderWidth: {
      'eael-cta-box-button': 'eael_cat_btn_normal_border_width',
      'eael-cta-box-container': 'eael_cta_border_width'
    },
    padding: {
      'eael-cta-box-container': 'eael_cta_container_padding',
    },
    margin: {
      'eael-cta-box-container': 'eael_cta_container_margin',
      'eael-cta-box-title': 'eael_cta_title_margin',
    }
  },
  'eael-dual-color-header': {
    typography: {
      'eael-dch-first-title': 'eael_dch_first_title_typography',
      'eael-dch-title': 'eael_dch_title_typography',
    },
    color: {
      'eael-dch-first-title': 'eael_dch_base_title_color',
      'eael-dch-title': 'eael_dch_title_color',
    },
    padding: {
      'eael-dch-container': 'eael_dch_container_padding',
    },
    margin: {
      'eael-dch-container': 'eael_dch_container_margin',
    },
    background: {
      'eael-dch-container': 'eael_dch_bg_color'
    }
  },
  'eael-feature-list': {
    typography: {
      'eael-feature-list-title': 'eael_feature_list_title_typography',
    },
    color: {
      'eael-feature-list-title': 'eael_feature_list_title_color',
    },
    background: {
      'eael-feature-list-icon': 'eael_feature_list_icon_background_color'
    },
    padding: {
      'eael-feature-list-icon': 'eael_feature_list_icon_padding',
    },
    margin: {
      'eael-feature-list-icon': 'eael_feature_list_icon_space',
      'eael-feature-list-list': 'eael_feature_list_space_between',
    }
  },
  'eael-info-box': {
    typography: {
      'eael-info-box-title': 'eael_infobox_title_typography',
      'eael-info-box-subtitle': 'eael_infobox_sub_title_typography',
      'eael-info-box-content': 'eael_infobox_content_typography_hover',
      'eael-info-box-button': 'eael_infobox_button_typography',
    },
    color: {
      'eael-info-box-title': 'eael_infobox_title_color',
      'eael-info-box-subtitle': 'eael_infobox_sub_title_color',
      'eael-info-box-content': 'eael_infobox_content_color',
      'eael-info-box-button': 'eael_infobox_button_text_color'
    },
    background: {
      'eael-info-box-container': 'eael_section_infobox_container_bg',
      'eael-info-box-button': 'eael_infobox_button_background_color'
    },
    borderRadius: {
      'eael-info-box-button': 'eael_infobox_button_border_radius',
      'eael-info-box-image': 'eael_infobox_img_shape_radius'
    },
    borderWidth: {
      'eael-info-box-button': 'eael_infobox_button_border_width'
    },
    padding: {
      'eael-info-box-container': 'eael_section_infobox_container_padding',
      'eael-info-box-button': 'eael_creative_button_padding'
    },
    margin: {
      'eael-info-box-title': 'eael_infobox_title_margin',
      'eael-info-box-subtitle': 'eael_infobox_subtitle_margin',
      'eael-info-box-content': 'eael_infobox_content_margin',
      'eael-info-box-image': 'eael_infobox_img_margin',
      'eael-info-box-main-content': 'parent-wrap'
    }
  },
  'eael-testimonial': {
    typography: {
      'eael-testimonial-name': 'eael_testimonial_name_typography',
      'eael-testimonial-company': 'eael_testimonial_position_typography',
      'eael-testimonial-content': 'eael_testimonial_description_typography',
    },
    color: {
      'eael-testimonial-name': 'eael_testimonial_name_color',
      'eael-testimonial-company': 'eael_testimonial_company_color',
      'eael-testimonial-content': 'eael_testimonial_description_color',
      'eael-testimonial-rating': 'eael_testimonial_rating_item_color',
    },
    background: {
      'eael-testimonial-container': 'eael_testimonial_background'
    },
    padding: {
      'eael-testimonial-container': 'eael_testimonial_padding',
    },
    margin: {
      'eael-testimonial-image': 'eael_testimonial_image_margin',
      'eael-testimonial-content': 'eael_testimonial_description_margin',
      'eael-testimonial-rating': 'eael_testimonial_rating_margin',
    }
  },
  'heading': {
    typography: {
      'heading-title': 'typography',
    }
  },
  'eael-post-carousel': {
    typography: {
      'eael-post-carousel-section-title': 'eael_carousel_title_typography',
      'eael-post-carousel-title': 'eael_post_grid_title_typography',
      'eael-post-carousel-content': 'eael_post_grid_excerpt_typography',
    },
    color: {
      'eael-post-carousel-section-title': 'eael_carousel_title_color',
      'eael-post-carousel-title': 'eael_post_grid_title_color',
      'eael-post-carousel-content': 'eael_post_grid_excerpt_color',
    },
    background: {
      'eael-post-carousel-container': 'eael_section_post_grid_container_background',
      'eael-post-carousel-dot': 'eael_post_grid_pagination_dot_color',
      'eael-post-carousel-active-dot': 'eael_post_grid_pagination_dot_active_color'
      // 'eael-post-carousel-post': 'eael_post_grid_bg_color'
    },
    borderRadius: {
      'eael-post-carousel-dot': 'eael_post_grid_pagination_dot_border_radius',
      'eael-post-carousel-active-dot': 'eael_post_grid_pagination_dot_active_border_radius'
    },
    // borderRadius: {
    //   'eael-post-carousel-post': 'eael_post_grid_border_radius',
    //   'eael-post-carousel-image': 'eael_thumbnail_border_radius'
    // },
    // padding: {
    //   'eael-post-carousel-post': 'eael_post_grid_padding',
    // },
    // margin: {
    //   'eael-post-carousel-post': 'eael_post_grid_margin',
    // }
  },
  'text-editor': {
    color: {
      'text-editor-text': 'text_color',
    }
  }
};

// Widget classes
export const widgetProcessors = {
  'button': ButtonWidgetProcessor,
  'eael-advanced-menu': AdvancedMenuWidgetProcessor,
  'eael-counter': CounterWidgetProcessor,
  'eael-creative-button': CreativeButtonWidgetProcessor,
  'eael-cta-box': CtaBoxWidgetProcessor,
  'eael-dual-color-header': DualColorHeadingWidgetProcessor,
  'eael-feature-list': FeatureListWidgetProcessor,
  'eael-info-box': InfoBoxWidgetProcessor,
  'eael-logo-carousel': LogoCarouselWidgetProcessor,
  'eael-post-carousel': PostCarouselWidgetProcessor,
  'eael-testimonial': TestimonialWidgetProcessor,
  'heading': HeadingWidgetProcessor,
  'image': ImageWidgetProcessor,
  'text-editor': TextEditorWidgetProcessor,
};

// For widgets
export const widgetDisplayNameMap: Record<string, string> = {
  'advanced-menu': 'eael-advanced-menu',
  'button': 'button',
  'counter': 'eael-counter',
  'creative-button': 'eael-creative-button',
  'cta-box': 'eael-cta-box',
  'call-to-action': 'eael-cta-box',
  'dual-color-header': 'eael-dual-color-header',
  'dual-color-heading': 'eael-dual-color-header',
  'feature-list': 'eael-feature-list',
  'heading': 'heading',
  'image': 'image',
  'info-box': 'eael-info-box',
  'logo-carousel': 'eael-logo-carousel',
  'post-carousel': 'eael-post-carousel',
  'testimonial': 'eael-testimonial',
  'text-editor': 'text-editor',
  // 'woo-product-list': 'eael-woo-product-list',
};

// For widget child items
export const displayNameMap: Record<string, string[]> = {
  'title': ['Heading', 'Title', 'Header', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6'],
  'section-title': ['Heading', 'Title', 'Header', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6'],
  'content': ['Content', 'Text Editor', 'Description', 'Paragraph', 'Text', 'Body'],
  'button': ['Button', 'Button-Primary', 'Button-Secondary', 'CTA Button', 'Submit'],
  'image': ['Image', 'Picture', 'Photo', 'Img', 'Featured Image'],
  'icon': ['Icon', 'FA Icon', 'Font Awesome', 'Symbol'],
  'subtitle': ['Subtitle', 'Sub title', 'Subheading'],
  'name': ['Name', 'Author', 'User Name', 'Heading', 'Title', 'Header', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6'],
  'company': ['Company', 'Organization', 'Business', 'Subheading', 'Subtitle'],
  'rating': ['Review', 'Rating', 'Stars', 'Star'],
  // 'controls': ['Navigation', 'Navigation Item', 'Control', 'Controls'],
  'dot': ['Dot', 'Bullet', 'Navigation', 'Pagination', 'Navigation Dot', 'Navigation Bullet'],
  'active-dot': ['Active Dot', 'Active Bullet', 'Active Navigation', 'Active Navigation Dot', 'Active Navigation Bullet', 'Dot Active', 'Bullet Active', 'Navigation Active', 'Navigation Dot Active'],
  'number': ['Number', 'Value', 'Digit'],
  'main-content': ['Main Content', 'Main Body', 'Primary Content'],
  'menu-item': ['Menu Item', 'MenuItem', 'Nav Item', 'Navigation Item', 'Menu'],
  'active-menu-item': ['Active Menu Item', 'ActiveMenuItem', 'Active Nav Item', 'Current Menu Item', 'Active Menu', 'Menu Active', 'Menu Item Active']
};